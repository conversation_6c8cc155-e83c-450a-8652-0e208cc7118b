# MCP Feedback Enhanced 安装指南

## 🐾 简介
mcp-feedback-enhanced 是一个增强的 MCP 服务器，用于在 AI 辅助开发中进行交互式用户反馈和命令执行。支持 Web UI 和桌面应用双界面。

## 📦 安装步骤

### 1. 安装 uv 包管理器
```bash
pip install uv
```

### 2. 测试安装
```bash
# 检查版本
uvx mcp-feedback-enhanced@latest version

# 测试 Web 界面
uvx mcp-feedback-enhanced@latest test --web

# 测试桌面应用
uvx mcp-feedback-enhanced@latest test --desktop
```

## ⚙️ 配置文件

### 基础配置 (mcp-config-basic.json)
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 桌面应用配置 (mcp-config-desktop.json)
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DESKTOP_MODE": "true",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false",
        "MCP_LANGUAGE": "zh-CN"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

## 🌟 主要特性
- 🖥️ **双界面支持**：Web UI 和桌面应用
- 📝 **智能工作流**：提示管理、自动提交、会话跟踪
- 🎨 **现代体验**：响应式设计、音频通知、多语言支持
- 🖼️ **图片支持**：支持拖拽上传、剪贴板粘贴

## 🔧 环境变量说明
- `MCP_DEBUG`: 调试模式 (true/false)
- `MCP_WEB_HOST`: Web UI 主机绑定 (默认: 127.0.0.1)
- `MCP_WEB_PORT`: Web UI 端口 (默认: 8765)
- `MCP_DESKTOP_MODE`: 桌面应用模式 (true/false)
- `MCP_LANGUAGE`: 强制界面语言 (zh-CN/zh-TW/en)

## 📱 使用方法
1. 将配置文件添加到你的 MCP 客户端
2. 重启 MCP 客户端
3. 在 AI 对话中调用 mcp-feedback-enhanced 工具
4. 系统会自动打开界面进行交互

## 🐛 常见问题
- 如果浏览器无法启动，检查端口是否被占用
- 如果是 SSH 远程环境，设置 `MCP_WEB_HOST` 为 `0.0.0.0`
- 如果出现中文乱码，更新到最新版本

## 📚 更多信息
- GitHub: https://github.com/Minidoracat/mcp-feedback-enhanced
- 文档: 查看项目 README 获取详细信息
