# ========================================
# DeathZombieV4 用户自定义实体系统配置文件
# ========================================
# 
# 配置说明：
# - 支持自定义IDC1-IDC22实体的属性、装备、技能等
# - 修改配置后使用 /dzs reload 重载
# - 使用 /czm other <实体ID> 生成自定义实体
# 
# 作者: Ver_zhzh
# 版本: 1.2.0
# 最后更新: 2025-01-30
# ========================================

# ========================================
# 系统总开关配置
# ========================================
system_settings:
  # 是否启用默认IDC实体设置（原有硬编码系统）
  use_default_settings: false

  # 是否启用用户自定义设置（新的配置驱动系统）
  use_user_custom_settings: true

  # 优先级策略：user_first=优先用户自定义，default_first=优先默认设置
  priority_strategy: "user_first"

  # 调试模式开关
  debug_mode: true

  # 是否在控制台输出详细日志
  verbose_logging: true

# ========================================
# 全局默认配置
# ========================================
global_defaults:
  # 全局默认生命值倍数
  health_multiplier: 1.0

  # 全局默认伤害倍数
  damage_multiplier: 1.0

  # 全局默认速度倍数
  speed_multiplier: 1.0

  # 全局默认名称前缀（支持颜色代码）
  name_prefix: "§c"

  # 全局默认名称后缀
  name_suffix: ""

  # 全局默认实体类型（当覆盖失败时使用）
  fallback_entity_type: "ZOMBIE"

  # 全局默认武器
  default_weapon: "WOODEN_SWORD"

  # 全局默认护甲材质
  default_armor_material: "LEATHER"

# ========================================
# 性能优化配置
# ========================================
performance:
  # 最大同时存在的用户自定义实体数量
  max_user_custom_entities: 50

  # 实体技能处理间隔（tick）
  skill_processing_interval: 20

  # 实体生成间隔限制（毫秒）
  entity_spawn_cooldown: 100

  # 是否启用配置缓存
  enable_config_cache: true

  # 配置文件重载间隔（秒）
  config_reload_interval: 300

# ========================================
# 兼容性配置
# ========================================
compatibility:
  # 与原有IDC系统的兼容模式
  legacy_compatibility: true

  # 是否保持原有的元数据标记
  preserve_original_metadata: true

  # 是否保持原有的命名规则
  preserve_original_naming: true

  # 是否启用实体类型回退
  enable_entity_type_fallback: true

  # 是否启用版本兼容检查
  enable_version_check: true

  # 支持的最低Bukkit版本
  min_bukkit_version: "1.16"

# ========================================
# 调试和日志配置
# ========================================
debug:
  # 是否记录实体生成日志
  log_entity_spawn: true

  # 是否记录配置加载日志
  log_config_loading: true

  # 是否记录技能执行日志
  log_skill_execution: true

  # 是否记录错误详情
  log_error_details: true

  # 日志级别（INFO, WARNING, SEVERE）
  log_level: "INFO"

  # 是否启用性能监控
  enable_performance_monitoring: false

  # 性能监控间隔（秒）
  performance_monitor_interval: 60

# ========================================
# 用户自定义覆盖配置
# ========================================
user_custom_overrides:
  # 全局覆盖设置
  global_overrides:
    # 是否启用全局覆盖
    enabled: false

    # 全局生命值覆盖（-1表示不覆盖）
    global_health_override: -1

    # 全局伤害覆盖（-1表示不覆盖）
    global_damage_override: -1

    # 全局速度倍数覆盖（-1表示不覆盖）
    global_speed_multiplier: -1

  # 特定实体ID的覆盖配置（IDC1-IDC22）
  specific_overrides:
    # IDC1 - 变异僵尸01（完整配置示例）
    idc1:
      enabled: true                    # 是否启用此覆盖配置
      health_override: 120.0          # 覆盖生命值
      damage_override: 10.0           # 覆盖伤害值
      speed_multiplier: 1.3           # 速度倍数
      custom_name_override: "§c§l强化变异僵尸"  # 覆盖显示名称
      entity_type_override: "ZOMBIFIED_PIGLIN"  # 覆盖实体类型

      # 装备覆盖配置
      weapon_override: "IRON_SWORD"   # 武器
      helmet_override: "LEATHER_HELMET"     # 头盔
      chestplate_override: "LEATHER_CHESTPLATE"  # 胸甲
      leggings_override: "LEATHER_LEGGINGS"      # 护腿
      boots_override: "LEATHER_BOOTS"            # 靴子

      # 武器附魔配置
      weapon_enchantments:
        SHARPNESS: 2                  # 锋利2
        FIRE_ASPECT: 1                # 火焰附加1

      # 护甲附魔配置
      helmet_enchantments:
        PROTECTION: 1                 # 保护1
      chestplate_enchantments:
        PROTECTION: 1                 # 保护1
      leggings_enchantments:
        PROTECTION: 1                 # 保护1
      boots_enchantments:
        PROTECTION: 1                 # 保护1

      # 药水效果配置
      potion_effects:
        # 永久速度效果
        speed:
          level: 1                    # 速度等级2（0-based）
          duration: -1                # 持续时间（-1表示永久）
        # 火焰抗性
        fire_resistance:
          level: 0                    # 火焰抗性1级
          duration: -1                # 永久

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        poison_duration: 80           # 剧毒持续时间（4秒）
        poison_level: 1               # 剧毒等级2（0-based）
        basic_attack: 15              # 基础攻击冷却时间（tick）

      # 特殊能力配置
      special_abilities:
        # 剧毒攻击配置
        poison_enabled: true          # 是否启用剧毒攻击
        poison_level: 1               # 剧毒等级2（0-based）
        poison_duration: 80           # 剧毒持续时间（4秒）
        poison_chance: 0.8            # 剧毒攻击概率（80%）

        # 粒子效果配置
        particle_enabled: true        # 是否启用粒子效果
        particle_interval: 5          # 粒子效果更新间隔（tick）

        # 抗性配置
        fire_resistance: true         # 火焰抗性
        knockback_resistance: 0.3     # 击退抗性（30%）

        # 攻击增强
        attack_damage_bonus: 2.0      # 额外攻击伤害
        critical_chance: 0.15         # 暴击概率（15%）
        critical_multiplier: 1.5      # 暴击倍数

    # IDC2 - 变异僵尸02（骷髅射手形态）
    idc2:
      enabled: true
      health_override: 100.0
      damage_override: 8.0
      speed_multiplier: 1.2
      custom_name_override: "§d§l变异僵尸02"
      entity_type_override: "SKELETON"

      # 装备配置
      weapon_override: "BOW"
      helmet_override: "CREEPER_HEAD"

      # 武器附魔配置
      weapon_enchantments:
        POWER: 2                      # 力量2
        PUNCH: 2                      # 冲击2

      # 药水效果配置
      potion_effects:
        speed:
          level: 1
          duration: -1
        jump_boost:
          level: 3
          duration: -1

      # 特殊能力配置
      special_abilities:
        particle_enabled: true
        particle_interval: 5

    # 注意：这里只显示了IDC1和IDC2的示例配置
    # 完整的IDC3-IDC22配置请参考原entity.yml文件

# ========================================
# 版本信息
# ========================================
version:
  # 配置文件版本
  config_version: "1.2.0"

  # 最后修改时间
  last_modified: "2025-01-30"

  # 作者信息
  author: "Ver_zhzh"

  # 兼容的插件版本
  compatible_plugin_version: "1.2+"
