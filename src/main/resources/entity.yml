# ========================================
# DeathZombieV4 用户自定义实体系统配置文件
# ========================================
# 
# 配置说明：
# - 支持自定义IDC1-IDC22实体的属性、装备、技能等
# - 修改配置后使用 /dzs reload 重载
# - 使用 /czm other <实体ID> 生成自定义实体
# 
# 作者: Ver_zhzh
# 版本: 1.2.0
# 最后更新: 2025-01-30
# ========================================

# ========================================
# 系统总开关配置
# ========================================
system_settings:
  # 是否启用默认IDC实体设置（原有硬编码系统）
  use_default_settings: false

  # 是否启用用户自定义设置（新的配置驱动系统）
  use_user_custom_settings: true

  # 调试模式开关
  debug_mode: true

# ========================================
# 全局默认配置
# ========================================
global_defaults:
  # 全局默认生命值倍数
  health_multiplier: 1.0

  # 全局默认伤害倍数
  damage_multiplier: 1.0

  # 全局默认速度倍数
  speed_multiplier: 1.0

  # 全局默认名称前缀（支持颜色代码）
  name_prefix: "§c"

  # 全局默认名称后缀
  name_suffix: ""

  # 全局默认实体类型（当覆盖失败时使用）
  fallback_entity_type: "ZOMBIE"

  # 全局默认武器
  default_weapon: "WOODEN_SWORD"

  # 全局默认护甲材质
  default_armor_material: "LEATHER"

# ========================================
# 性能优化配置
# ========================================
performance:
  # 最大同时存在的用户自定义实体数量
  max_user_custom_entities: 50

  # 实体技能处理间隔（tick）
  skill_processing_interval: 20

  # 实体生成间隔限制（毫秒）
  entity_spawn_cooldown: 100

  # 是否启用配置缓存
  enable_config_cache: true

  # 配置文件重载间隔（秒）
  config_reload_interval: 300

# ========================================
# 兼容性配置
# ========================================
compatibility:
  # 与原有IDC系统的兼容模式
  legacy_compatibility: true

  # 是否保持原有的元数据标记
  preserve_original_metadata: true

  # 是否保持原有的命名规则
  preserve_original_naming: true

  # 是否启用实体类型回退
  enable_entity_type_fallback: true

  # 是否启用版本兼容检查
  enable_version_check: true

  # 支持的最低Bukkit版本
  min_bukkit_version: "1.16"

# ========================================
# 调试和日志配置
# ========================================
debug:
  # 是否记录实体生成日志
  log_entity_spawn: true

  # 是否记录配置加载日志
  log_config_loading: true

  # 是否记录技能执行日志
  log_skill_execution: true

  # 是否记录错误详情
  log_error_details: true

  # 日志级别（INFO, WARNING, SEVERE）
  log_level: "INFO"

  # 是否启用性能监控
  enable_performance_monitoring: false

  # 性能监控间隔（秒）
  performance_monitor_interval: 60

# ========================================
# 用户自定义覆盖配置
# ========================================
user_custom_overrides:
  # 全局覆盖设置
  global_overrides:
    # 是否启用全局覆盖
    enabled: false

    # 全局生命值覆盖（-1表示不覆盖）
    global_health_override: -1

    # 全局伤害覆盖（-1表示不覆盖）
    global_damage_override: -1

    # 全局速度倍数覆盖（-1表示不覆盖）
    global_speed_multiplier: -1

  # 特定实体ID的覆盖配置（IDC1-IDC22）
  specific_overrides:
    # IDC1 - 变异僵尸01（完整配置示例）
    idc1:
      enabled: true                    # 是否启用此覆盖配置
      health_override: 120.0          # 覆盖生命值
      damage_override: 10.0           # 覆盖伤害值
      speed_multiplier: 1.3           # 速度倍数
      custom_name_override: "§c§l强化变异僵尸"  # 覆盖显示名称
      entity_type_override: "ZOMBIFIED_PIGLIN"  # 覆盖实体类型

      # 装备覆盖配置
      weapon_override: "IRON_SWORD"   # 武器
      helmet_override: "LEATHER_HELMET"     # 头盔
      chestplate_override: "LEATHER_CHESTPLATE"  # 胸甲
      leggings_override: "LEATHER_LEGGINGS"      # 护腿
      boots_override: "LEATHER_BOOTS"            # 靴子

      # 武器附魔配置
      weapon_enchantments:
        SHARPNESS: 2                  # 锋利2
        FIRE_ASPECT: 1                # 火焰附加1

      # 护甲附魔配置
      helmet_enchantments:
        PROTECTION: 1                 # 保护1
      chestplate_enchantments:
        PROTECTION: 1                 # 保护1
      leggings_enchantments:
        PROTECTION: 1                 # 保护1
      boots_enchantments:
        PROTECTION: 1                 # 保护1

      # 药水效果配置
      potion_effects:
        # 永久速度效果
        speed:
          level: 1                    # 速度等级2（0-based）
          duration: -1                # 持续时间（-1表示永久）
        # 火焰抗性
        fire_resistance:
          level: 0                    # 火焰抗性1级
          duration: -1                # 永久

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        poison_duration: 80           # 剧毒持续时间（4秒）
        poison_level: 1               # 剧毒等级2（0-based）
        basic_attack: 15              # 基础攻击冷却时间（tick）

      # 特殊能力配置
      special_abilities:
        # 剧毒攻击配置
        poison_enabled: true          # 是否启用剧毒攻击
        poison_level: 1               # 剧毒等级2（0-based）
        poison_duration: 80           # 剧毒持续时间（4秒）
        poison_chance: 0.8            # 剧毒攻击概率（80%）

        # 粒子效果配置
        particle_enabled: true        # 是否启用粒子效果
        particle_interval: 5          # 粒子效果更新间隔（tick）

        # 抗性配置
        fire_resistance: true         # 火焰抗性
        knockback_resistance: 0.3     # 击退抗性（30%）

        # 攻击增强
        attack_damage_bonus: 2.0      # 额外攻击伤害
        critical_chance: 0.15         # 暴击概率（15%）
        critical_multiplier: 1.5      # 暴击倍数

    # IDC2 - 变异僵尸02（骷髅射手形态）
    idc2:
      enabled: true
      health_override: 100.0
      damage_override: 8.0
      speed_multiplier: 1.2
      custom_name_override: "§d§l变异僵尸02"
      entity_type_override: "SKELETON"

      # 装备配置
      weapon_override: "BOW"
      helmet_override: "CREEPER_HEAD"

      # 武器附魔配置
      weapon_enchantments:
        POWER: 2                      # 力量2
        PUNCH: 2                      # 冲击2

      # 药水效果配置
      potion_effects:
        speed:
          level: 1
          duration: -1
        jump_boost:
          level: 3
          duration: -1

      # 特殊能力配置
      special_abilities:
        particle_enabled: true
        particle_interval: 5

    # IDC3 - 变异烈焰人
    idc3:
      enabled: true
      health_override: 120.0
      damage_override: 8.0
      custom_name_override: "§6§l变异烈焰人"
      entity_type_override: "BLAZE"

      # 药水效果配置
      potion_effects:
        speed:
          level: 3                    # 速度4
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        attack_interval: 60           # 攻击间隔（3秒）
        particle_damage: 4            # 粒子伤害
        fireball_damage: 10           # 烈焰弹伤害
        attack_range: 15.0            # 攻击范围

      # 特殊能力配置
      special_abilities:
        # 烈焰粒子攻击
        particle_attack_enabled: true
        # 烈焰弹攻击
        fireball_attack_enabled: true

    # IDC4 - 变异爬行者
    idc4:
      enabled: true
      health_override: 50.0
      custom_name_override: "§b§l变异爬行者"
      entity_type_override: "CREEPER"

      # 药水效果配置
      potion_effects:
        speed:
          level: 5                    # 速度6
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        lightning_interval: 100       # 闪电攻击间隔（5秒）
        lightning_range: 20.0         # 闪电攻击范围
        explosion_radius: 8           # 爆炸半径

      # 特殊能力配置
      special_abilities:
        lightning_enabled: true       # 启用闪电攻击
        prevent_block_damage: true    # 防止破坏方块

    # IDC5 - 变异蜘蛛
    idc5:
      enabled: true
      health_override: 80.0
      damage_override: 6.0
      custom_name_override: "§8§l变异蜘蛛"
      entity_type_override: "SPIDER"

      # 药水效果配置
      potion_effects:
        speed:
          level: 2                    # 速度3
          duration: -1
        jump_boost:
          level: 4                    # 跳跃提升5
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        web_interval: 60              # 蛛网攻击间隔（3秒）
        web_range: 10.0               # 蛛网攻击范围
        poison_duration: 100          # 剧毒持续时间（5秒）

      # 特殊能力配置
      special_abilities:
        web_attack_enabled: true      # 启用蛛网攻击
        poison_attack_enabled: true   # 启用剧毒攻击

    # IDC6 - 变异末影人
    idc6:
      enabled: true
      health_override: 200.0
      damage_override: 12.0
      custom_name_override: "§5§l变异末影人"
      entity_type_override: "ENDERMAN"

      # 药水效果配置
      potion_effects:
        speed:
          level: 1                    # 速度2
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        teleport_interval: 40         # 瞬移攻击间隔（2秒）
        teleport_range: 15.0          # 瞬移范围
        particle_damage: 8            # 粒子伤害

      # 特殊能力配置
      special_abilities:
        teleport_attack_enabled: true # 启用瞬移攻击
        particle_attack_enabled: true # 启用粒子攻击

    # IDC7 - 灾厄卫道士
    idc7:
      enabled: true
      health_override: 300.0
      damage_override: 15.0
      custom_name_override: "§0§l变异凋灵骷髅"
      entity_type_override: "WITHER_SKELETON"

      # 装备配置
      weapon_override: "NETHERITE_SWORD"
      helmet_override: "WITHER_SKELETON_SKULL"

      # 武器附魔配置
      weapon_enchantments:
        SHARPNESS: 3
        FIRE_ASPECT: 2

      # 药水效果配置
      potion_effects:
        speed:
          level: 1                    # 速度2
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        wither_skull_interval: 80     # 凋灵头颅攻击间隔（4秒）
        wither_effect_duration: 100  # 凋零效果持续时间（5秒）

      # 特殊能力配置
      special_abilities:
        wither_skull_enabled: true    # 启用凋灵头颅攻击
        wither_effect_enabled: true   # 启用凋零效果

    # IDC8 - 灾厄唤魔者
    idc8:
      enabled: true
      health_override: 600.0
      damage_override: 12.0
      custom_name_override: "§5§l灾厄唤魔者"
      entity_type_override: "EVOKER"

      # 药水效果配置
      potion_effects:
        speed:
          level: 1                    # 速度2
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        summon_interval: 10000        # 召唤间隔（10秒）
        magic_attack_interval: 2000   # 魔法攻击间隔（2秒）
        max_minions: 10               # 最大仆从数量

      # 特殊能力配置
      special_abilities:
        circle_enabled: true          # 启用脚底粒子圆圈
        circle_radius: 2.0            # 圆圈半径
        summon_enabled: true          # 启用召唤卫道士
        magic_attack_enabled: true    # 启用DNA螺旋魔法攻击

    # IDC9 - 灾厄劫掠兽
    idc9:
      enabled: true
      health_override: 1000.0
      damage_override: 20.0
      custom_name_override: "§4§l灾厄劫掠兽"
      entity_type_override: "RAVAGER"

      # 药水效果配置
      potion_effects:
        jump_boost:
          level: 2                    # 跳跃提升3
          duration: -1
        speed:
          level: 0                    # 速度1
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        circle_damage: 2.0            # 圆圈伤害
        knockback_strength: 2.0       # 击退强度

      # 特殊能力配置
      special_abilities:
        circle_attack_enabled: true   # 启用脚底暴击圆圈
        circle_radius: 3.0            # 圆圈半径

    # IDC10 - 变异僵尸马
    idc10:
      enabled: true
      health_override: 300.0
      damage_override: 10.0
      custom_name_override: "§2§l变异僵尸马"
      entity_type_override: "ZOMBIE_HORSE"

      # 药水效果配置
      potion_effects:
        speed:
          level: 4                    # 速度5
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        collision_damage: 8.0         # 撞击伤害
        summon_interval: 5000         # 召唤间隔（5秒）
        max_zombies: 5                # 最大僵尸数量
        tracking_range: 30.0          # 追踪范围

      # 特殊能力配置
      special_abilities:
        collision_enabled: true       # 启用撞击伤害
        summon_enabled: true          # 启用召唤武装僵尸
        particle_enabled: true        # 启用持续粒子效果
        tracking_enabled: true        # 启用智能追踪移动
        flying_enabled: true          # 启用飞行能力

    # IDC11 - 变异岩浆怪
    idc11:
      enabled: true
      health_override: 400.0
      damage_override: 14.0
      custom_name_override: "§3§l变异守卫者"
      entity_type_override: "GUARDIAN"

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        laser_damage: 12.0            # 激光伤害
        laser_interval: 60            # 激光攻击间隔（3秒）

      # 特殊能力配置
      special_abilities:
        laser_attack_enabled: true    # 启用激光攻击

    # IDC12 - 变异骷髅
    idc12:
      enabled: true
      health_override: 800.0
      damage_override: 18.0
      custom_name_override: "§b§l变异远古守卫者"
      entity_type_override: "ELDER_GUARDIAN"

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        laser_damage: 15.0            # 激光伤害
        laser_interval: 40            # 激光攻击间隔（2秒）
        mining_fatigue_duration: 200 # 挖掘疲劳持续时间（10秒）

      # 特殊能力配置
      special_abilities:
        laser_attack_enabled: true    # 启用激光攻击
        mining_fatigue_enabled: true  # 启用挖掘疲劳效果

    # IDC13 - 变异僵尸3
    idc13:
      enabled: true
      health_override: 1500.0
      damage_override: 25.0
      custom_name_override: "§8§l变异凋灵"
      entity_type_override: "WITHER"

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        skull_damage: 20.0            # 凋灵头颅伤害
        skull_interval: 40            # 头颅攻击间隔（2秒）
        explosion_radius: 6           # 爆炸半径

      # 特殊能力配置
      special_abilities:
        skull_attack_enabled: true    # 启用凋灵头颅攻击
        explosion_enabled: true       # 启用爆炸攻击

    # IDC14 - 变异僵尸04
    idc14:
      enabled: true
      health_override: 2000.0
      damage_override: 30.0
      custom_name_override: "§d§l变异末影龙"
      entity_type_override: "ENDER_DRAGON"

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        breath_damage: 25.0           # 龙息伤害
        breath_interval: 80           # 龙息攻击间隔（4秒）
        fireball_damage: 20.0         # 火球伤害

      # 特殊能力配置
      special_abilities:
        breath_attack_enabled: true   # 启用龙息攻击
        fireball_attack_enabled: true # 启用火球攻击
        flying_enabled: true          # 启用飞行能力

    # IDC15 - 鲜血猪灵
    idc15:
      enabled: true
      health_override: 150.0
      damage_override: 8.0
      custom_name_override: "§9§l变异幻翼"
      entity_type_override: "PHANTOM"

      # 药水效果配置
      potion_effects:
        speed:
          level: 2                    # 速度3
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        dive_damage: 10.0             # 俯冲伤害
        dive_interval: 100            # 俯冲攻击间隔（5秒）

      # 特殊能力配置
      special_abilities:
        dive_attack_enabled: true     # 启用俯冲攻击
        flying_enabled: true          # 启用飞行能力

    # IDC16 - 暗影潜影贝
    idc16:
      enabled: true
      health_override: 250.0
      damage_override: 12.0
      custom_name_override: "§7§l变异掠夺者"
      entity_type_override: "PILLAGER"

      # 装备配置
      weapon_override: "CROSSBOW"

      # 武器附魔配置
      weapon_enchantments:
        PIERCING: 2                   # 穿透2
        QUICK_CHARGE: 3               # 快速装填3

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        arrow_damage: 8.0             # 箭矢伤害
        rapid_fire_interval: 20       # 连射间隔（1秒）

      # 特殊能力配置
      special_abilities:
        rapid_fire_enabled: true      # 启用连射攻击

    # IDC17 - 变异雪傀儡
    idc17:
      enabled: true
      health_override: 350.0
      damage_override: 16.0
      custom_name_override: "§e§l变异卫道士"
      entity_type_override: "VINDICATOR"

      # 装备配置
      weapon_override: "DIAMOND_AXE"

      # 武器附魔配置
      weapon_enchantments:
        SHARPNESS: 4                  # 锋利4
        KNOCKBACK: 2                  # 击退2

      # 药水效果配置
      potion_effects:
        strength:
          level: 1                    # 力量2
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        charge_damage: 20.0           # 冲锋伤害
        charge_interval: 120          # 冲锋间隔（6秒）

      # 特殊能力配置
      special_abilities:
        charge_attack_enabled: true   # 启用冲锋攻击

    # IDC18 - 变异铁傀儡
    idc18:
      enabled: true
      health_override: 500.0
      damage_override: 18.0
      custom_name_override: "§1§l变异监守者"
      entity_type_override: "WARDEN"

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        sonic_boom_damage: 30.0       # 音爆伤害
        sonic_boom_interval: 100      # 音爆间隔（5秒）
        darkness_duration: 200        # 黑暗效果持续时间（10秒）

      # 特殊能力配置
      special_abilities:
        sonic_boom_enabled: true      # 启用音爆攻击
        darkness_enabled: true        # 启用黑暗效果

    # IDC19 - 变异僵尸Max
    idc19:
      enabled: true
      health_override: 400.0
      damage_override: 20.0
      custom_name_override: "§c§l变异猪灵蛮兵"
      entity_type_override: "PIGLIN_BRUTE"

      # 装备配置
      weapon_override: "NETHERITE_AXE"

      # 武器附魔配置
      weapon_enchantments:
        SHARPNESS: 3                  # 锋利3
        FIRE_ASPECT: 2                # 火焰附加2

      # 药水效果配置
      potion_effects:
        strength:
          level: 2                    # 力量3
          duration: -1
        fire_resistance:
          level: 0                    # 火焰抗性
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        berserker_duration: 100       # 狂暴持续时间（5秒）
        berserker_interval: 200       # 狂暴间隔（10秒）

      # 特殊能力配置
      special_abilities:
        berserker_enabled: true       # 启用狂暴模式

    # IDC20 - 灵魂坚守者
    idc20:
      enabled: true
      health_override: 600.0
      damage_override: 22.0
      custom_name_override: "§4§l变异疣猪兽"
      entity_type_override: "HOGLIN"

      # 药水效果配置
      potion_effects:
        strength:
          level: 1                    # 力量2
          duration: -1
        speed:
          level: 0                    # 速度1
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        charge_damage: 25.0           # 冲撞伤害
        charge_interval: 80           # 冲撞间隔（4秒）
        knockback_strength: 3.0       # 击退强度

      # 特殊能力配置
      special_abilities:
        charge_attack_enabled: true   # 启用冲撞攻击

    # IDC21 - 凋零领主
    idc21:
      enabled: true
      health_override: 700.0
      damage_override: 24.0
      custom_name_override: "§8§l变异僵尸疣猪兽"
      entity_type_override: "ZOGLIN"

      # 药水效果配置
      potion_effects:
        strength:
          level: 2                    # 力量3
          duration: -1
        speed:
          level: 1                    # 速度2
          duration: -1

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        charge_damage: 28.0           # 冲撞伤害
        charge_interval: 60           # 冲撞间隔（3秒）
        poison_duration: 80           # 剧毒持续时间（4秒）

      # 特殊能力配置
      special_abilities:
        charge_attack_enabled: true   # 启用冲撞攻击
        poison_attack_enabled: true   # 启用剧毒攻击

    # IDC22 - 异变之王（末影龙形态）
    idc22:
      enabled: true
      health_override: 10999.0
      damage_override: 20.0
      custom_name_override: "§4异变之王"
      entity_type_override: "ENDER_DRAGON"

      # 技能冷却时间覆盖
      skill_cooldown_overrides:
        # 基础配置
        movement_interval: 5          # 移动AI间隔（5tick=0.25秒）
        attack_interval: 10           # 近战攻击间隔（10tick=0.5秒）

        # 技能冷却配置
        crystal_attack_cooldown: 15000    # 末影水晶攻击冷却（15秒）
        breath_attack_cooldown: 10000     # 龙息攻击冷却（10秒）
        obsidian_attack_cooldown: 5000    # 黑曜石柱攻击冷却（5秒）
        obsidian_blocks_cooldown: 8000    # 黑曜石块攻击冷却（8秒）
        nether_fence_cooldown: 6000       # 下界栅栏攻击冷却（6秒）
        ender_field_cooldown: 20000       # 末影粒子场冷却（20秒）
        summon_cooldown: 4000             # 召唤变异生物冷却（4秒）

        # 黑曜石柱攻击配置
        obsidian_pillar_count: 3          # 黑曜石柱数量（3个）
        obsidian_pillar_random_range: 8   # 随机柱子位置范围（8格）

        # 黑曜石块攻击配置
        obsidian_blocks_waves: 5          # 攻击波数（5波）
        obsidian_blocks_per_wave: 7       # 每波方块数量（7个）
        obsidian_blocks_wave_interval: 40 # 波次间隔（40tick=2秒）
        obsidian_blocks_instant_cleanup: true  # 立即清理（原版行为）
        obsidian_blocks_cleanup_delay: 0  # 清理延迟（0=立即）
        obsidian_blocks_speed: 15         # 发射速度（15=1.5倍）
        obsidian_blocks_spread: 6         # 散布范围（6格）

      # 特殊能力配置
      special_abilities:
        # 技能开关
        movement_ai_enabled: true         # 智能移动AI
        melee_attack_enabled: true        # 近战攻击
        crystal_attack_enabled: true      # 末影水晶攻击
        breath_attack_enabled: true       # 龙息攻击
        obsidian_attack_enabled: true     # 黑曜石攻击
        fence_attack_enabled: true        # 下界栅栏攻击
        ender_field_enabled: true         # 末影粒子场
        summon_enabled: true              # 召唤变异生物
        boss_bar_enabled: true            # Boss血条
        hostile_ai_enabled: true          # 敌对AI

# ========================================
# 版本信息
# ========================================
version:
  # 配置文件版本
  config_version: "1.2.0"

  # 最后修改时间
  last_modified: "2025-01-30"

  # 作者信息
  author: "Ver_zhzh"

  # 兼容的插件版本
  compatible_plugin_version: "1.2+"
