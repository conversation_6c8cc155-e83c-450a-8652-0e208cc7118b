package org.Ver_zhzh.deathZombieV4.commands;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.ZombieHelper;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.configuration.file.YamlConfiguration;
import java.io.File;
import java.util.Map;

/**
 * 处理/czm命令的执行器类
 */
public class CZMCommandExecutor implements CommandExecutor {

    private final DeathZombieV4 plugin;
    private final ZombieHelper zombieHelper;
    private final CZMTabCompleter tabCompleter;

    /**
     * 构造函数
     *
     * @param plugin DeathZombieV4插件实例
     */
    public CZMCommandExecutor(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.zombieHelper = plugin.getZombieHelper();
        this.tabCompleter = new CZMTabCompleter(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 允许非玩家（控制台、命令方块等）使用此命令
        Player player = null;
        if (sender instanceof Player) {
            player = (Player) sender;
        }

        if (args.length < 1) {
            sendHelpMessage(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "zombie":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定僵尸名称或ID! 用法: /czm zombie <名称|ID>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm zombie 普通僵尸 或 /czm zombie id1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String zombieInput = args[1];
                String zombieId = convertZombieNameToId(zombieInput);

                if (zombieId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的僵尸名称或ID: " + zombieInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的僵尸名称");
                    return true;
                }

                Zombie zombie = zombieHelper.spawnCustomZombie(player.getLocation(), zombieId);

                if (zombie != null) {
                    String zombieDisplayName = getZombieDisplayName(zombieInput, zombieId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成自定义僵尸: " + zombieDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成自定义僵尸失败");
                }
                return true;

            case "other":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定实体名称或ID! 用法: /czm other <名称|IDC>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm other 变异僵尸01 或 /czm other idc1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String entityInput = args[1];
                String entityId = convertEntityNameToId(entityInput);

                if (entityId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的实体名称或ID: " + entityInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的实体名称");
                    return true;
                }

                // 调用CustomZombie的spawnOtherEntity方法，传入Player参数
                zombieHelper.getCustomZombie().spawnOtherEntity(player, entityId);
                String entityDisplayName = getEntityDisplayName(entityInput, entityId);
                sender.sendMessage(ChatColor.GREEN + "成功生成实体: " + entityDisplayName);
                return true;

            case "npc":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定NPC名称或ID! 用法: /czm npc <名称|IDN>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                // 检查Citizens插件是否可用
                if (Bukkit.getPluginManager().getPlugin("Citizens") == null) {
                    sender.sendMessage(ChatColor.RED + "无法生成NPC! 服务器未安装Citizens插件。");
                    return true;
                }

                String npcInput = args[1];
                String npcId = convertNpcNameToId(npcInput);

                if (npcId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的NPC名称或ID: " + npcInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的NPC名称");
                    return true;
                }

                boolean npcSuccess = spawnNPC(player, npcId);

                if (npcSuccess) {
                    String npcDisplayName = getNpcDisplayName(npcInput, npcId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成NPC: " + npcDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成NPC失败: " + npcId);
                }
                return true;

            case "gui":
                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要打开GUI界面!");
                    return true;
                }

                // 尝试打开GUI菜单
                boolean guiOpened = zombieHelper.openGUI(player);
                if (!guiOpened) {
                    sender.sendMessage(ChatColor.RED + "GUI管理器未初始化，无法打开GUI界面");
                }
                return true;

            case "spawnmode":
                if (args.length < 4) {
                    sender.sendMessage(ChatColor.RED + "用法: /czm spawnmode <zombie|entity> <user|normal> <on|off>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm spawnmode zombie user on");
                    return true;
                }

                // 权限检查
                if (sender instanceof Player && !sender.hasPermission("deathzombie.admin")) {
                    sender.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }

                return handleSpawnModeCommand(sender, args[1], args[2], args[3]);

            case "help":
            default:
                sendHelpMessage(sender);
                return true;
        }
    }

    /**
     * 根据NPC ID生成相应的NPC
     *
     * @param player 玩家
     * @param npcId NPC ID
     * @return 是否成功生成
     */
    private boolean spawnNPC(Player player, String npcId) {
        switch (npcId) {
            case "idn1":
                // 感染者1
                return zombieHelper.createInfectedNPC1(player.getLocation());
            case "idn2":
                // 感染者2号
                return zombieHelper.createInfectedNPC2(player.getLocation());
            case "idn3":
                // 感染者农民
                return zombieHelper.createInfectedFarmer(player.getLocation());
            case "idn4":
                // 感染者居民
                return zombieHelper.createInfectedResident(player.getLocation());
            case "idn5":
                // 感染猪
                return zombieHelper.createInfectedPig(player.getLocation());
            default:
                return false;
        }
    }

    /**
     * 发送帮助信息
     *
     * @param sender 命令发送者
     */
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "======= CustomZombie 帮助 =======");
        sender.sendMessage(ChatColor.GREEN + "/czm zombie <名称|ID> - 生成指定的僵尸 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 普通僵尸, 小僵尸, 路障僵尸, 钻斧僵尸...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm zombie 普通僵尸 或 /czm zombie id1");
        sender.sendMessage(ChatColor.GREEN + "/czm other <名称|IDC> - 生成指定的其他实体 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 变异僵尸01, 变异僵尸02, 变异烈焰人...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm other 变异僵尸01 或 /czm other idc1");
        sender.sendMessage(ChatColor.GREEN + "/czm npc <名称|IDN> - 生成指定的NPC (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 感染者史蒂夫, 感染者艾利克斯, 感染者农民...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
        sender.sendMessage(ChatColor.GREEN + "/czm gui - 打开自定义僵尸GUI菜单 (需要玩家执行)");
        sender.sendMessage(ChatColor.GREEN + "/czm spawnmode <zombie|entity> <user|normal> <on|off> - 快速切换生成模式");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm spawnmode zombie user on (启用僵尸用户自定义模式)");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm spawnmode entity normal on (启用实体默认模式)");
        sender.sendMessage(ChatColor.GREEN + "/czm help - 显示帮助信息");
        sender.sendMessage(ChatColor.GRAY + "提示: 使用Tab键可以自动补全中文名称");
    }

    /**
     * 转换僵尸名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertZombieNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("id")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getZombieIdByName(input);
        return id;
    }

    /**
     * 转换实体名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertEntityNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idc")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getEntityIdByName(input);
        return id;
    }

    /**
     * 转换NPC名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertNpcNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idn")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getNpcIdByName(input);
        return id;
    }

    /**
     * 获取僵尸显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getZombieDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("id")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.zombieNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取实体显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getEntityDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idc")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.entityNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取NPC显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getNpcDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idn")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.npcNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 处理生成模式切换命令
     *
     * @param sender 命令发送者
     * @param systemType 系统类型 (zombie|entity)
     * @param modeType 模式类型 (user|normal)
     * @param action 操作 (on|off)
     * @return 命令是否成功执行
     */
    private boolean handleSpawnModeCommand(CommandSender sender, String systemType, String modeType, String action) {
        // 参数验证
        systemType = systemType.toLowerCase();
        modeType = modeType.toLowerCase();
        action = action.toLowerCase();

        if (!systemType.equals("zombie") && !systemType.equals("entity")) {
            sender.sendMessage(ChatColor.RED + "无效的系统类型！请使用 'zombie' 或 'entity'");
            return true;
        }

        if (!modeType.equals("user") && !modeType.equals("normal")) {
            sender.sendMessage(ChatColor.RED + "无效的模式类型！请使用 'user' 或 'normal'");
            return true;
        }

        if (!action.equals("on") && !action.equals("off")) {
            sender.sendMessage(ChatColor.RED + "无效的操作！请使用 'on' 或 'off'");
            return true;
        }

        try {
            boolean success = false;
            String configFile = "";

            if (systemType.equals("zombie")) {
                success = updateZombieConfig(modeType, action);
                configFile = "zombie.yml";
            } else {
                success = updateEntityConfig(modeType, action);
                configFile = "entity.yml";
            }

            if (success) {
                sender.sendMessage(ChatColor.GREEN + "成功更新 " + configFile + " 配置！");
                sender.sendMessage(ChatColor.YELLOW + "系统: " + systemType.toUpperCase() +
                                 ", 模式: " + modeType.toUpperCase() +
                                 ", 状态: " + action.toUpperCase());

                // 显示当前配置状态
                showCurrentConfig(sender, systemType);
            } else {
                sender.sendMessage(ChatColor.RED + "更新配置失败！请检查配置文件是否存在。");
            }

        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "更新配置时发生错误: " + e.getMessage());
            plugin.getLogger().warning("SpawnMode命令执行失败: " + e.getMessage());
        }

        return true;
    }

    /**
     * 更新僵尸系统配置
     *
     * @param modeType 模式类型 (user|normal)
     * @param action 操作 (on|off)
     * @return 是否成功
     */
    private boolean updateZombieConfig(String modeType, String action) {
        try {
            File zombieConfigFile = new File(plugin.getDataFolder(), "zombie.yml");
            if (!zombieConfigFile.exists()) {
                plugin.getLogger().warning("zombie.yml 文件不存在");
                return false;
            }

            YamlConfiguration config = YamlConfiguration.loadConfiguration(zombieConfigFile);

            boolean enableValue = action.equals("on");

            if (modeType.equals("user")) {
                config.set("system_settings.use_user_custom_settings", enableValue);
                // 如果启用用户模式，禁用默认模式以避免冲突
                if (enableValue) {
                    config.set("system_settings.use_default_settings", false);
                }
                // 如果禁用用户模式，检查是否需要启用默认模式
                else {
                    boolean currentDefaultEnabled = config.getBoolean("system_settings.use_default_settings", false);
                    if (!currentDefaultEnabled) {
                        // 两个模式都为false时，自动启用默认模式作为备选
                        config.set("system_settings.use_default_settings", true);
                    }
                }
            } else { // normal
                config.set("system_settings.use_default_settings", enableValue);
                // 如果启用默认模式，禁用用户模式以避免冲突
                if (enableValue) {
                    config.set("system_settings.use_user_custom_settings", false);
                }
                // 如果禁用默认模式，检查是否需要启用用户模式
                else {
                    boolean currentUserEnabled = config.getBoolean("system_settings.use_user_custom_settings", false);
                    if (!currentUserEnabled) {
                        // 两个模式都为false时，自动启用用户模式作为备选
                        config.set("system_settings.use_user_custom_settings", true);
                    }
                }
            }

            // 保存配置文件
            config.save(zombieConfigFile);

            // 重载配置让修改生效
            if (zombieHelper != null) {
                try {
                    zombieHelper.reloadDualSystemConfig();
                } catch (Exception e) {
                    plugin.getLogger().warning("重载僵尸系统配置时出错: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("更新zombie.yml配置时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 更新实体系统配置
     *
     * @param modeType 模式类型 (user|normal)
     * @param action 操作 (on|off)
     * @return 是否成功
     */
    private boolean updateEntityConfig(String modeType, String action) {
        try {
            File entityConfigFile = new File(plugin.getDataFolder(), "entity.yml");
            if (!entityConfigFile.exists()) {
                plugin.getLogger().warning("entity.yml 文件不存在");
                return false;
            }

            YamlConfiguration config = YamlConfiguration.loadConfiguration(entityConfigFile);

            boolean enableValue = action.equals("on");

            if (modeType.equals("user")) {
                config.set("system_settings.use_user_custom_settings", enableValue);
                // 如果启用用户模式，禁用默认模式以避免冲突
                if (enableValue) {
                    config.set("system_settings.use_default_settings", false);
                }
                // 如果禁用用户模式，检查是否需要启用默认模式
                else {
                    boolean currentDefaultEnabled = config.getBoolean("system_settings.use_default_settings", false);
                    if (!currentDefaultEnabled) {
                        // 两个模式都为false时，自动启用默认模式作为备选
                        config.set("system_settings.use_default_settings", true);
                    }
                }
            } else { // normal
                config.set("system_settings.use_default_settings", enableValue);
                // 如果启用默认模式，禁用用户模式以避免冲突
                if (enableValue) {
                    config.set("system_settings.use_user_custom_settings", false);
                }
                // 如果禁用默认模式，检查是否需要启用用户模式
                else {
                    boolean currentUserEnabled = config.getBoolean("system_settings.use_user_custom_settings", false);
                    if (!currentUserEnabled) {
                        // 两个模式都为false时，自动启用用户模式作为备选
                        config.set("system_settings.use_user_custom_settings", true);
                    }
                }
            }

            // 保存配置文件
            config.save(entityConfigFile);

            // 重载配置让修改生效
            if (zombieHelper != null && zombieHelper.getCustomZombie() != null &&
                zombieHelper.getCustomZombie().getEntityIntegration() != null) {
                try {
                    zombieHelper.getCustomZombie().getEntityIntegration().reloadConfig();
                } catch (Exception e) {
                    plugin.getLogger().warning("重载实体系统配置时出错: " + e.getMessage());
                }
            }

            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("更新entity.yml配置时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 显示当前配置状态
     *
     * @param sender 命令发送者
     * @param systemType 系统类型
     */
    private void showCurrentConfig(CommandSender sender, String systemType) {
        try {
            String configFileName = systemType.equals("zombie") ? "zombie.yml" : "entity.yml";
            File configFile = new File(plugin.getDataFolder(), configFileName);

            if (!configFile.exists()) {
                sender.sendMessage(ChatColor.RED + configFileName + " 文件不存在！");
                return;
            }

            YamlConfiguration config = YamlConfiguration.loadConfiguration(configFile);

            boolean useDefault = config.getBoolean("system_settings.use_default_settings", false);
            boolean useUserCustom = config.getBoolean("system_settings.use_user_custom_settings", false);

            sender.sendMessage(ChatColor.GOLD + "========== " + systemType.toUpperCase() + " 系统当前配置 ==========");
            sender.sendMessage(ChatColor.YELLOW + "默认模式 (Normal): " + (useDefault ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用"));
            sender.sendMessage(ChatColor.YELLOW + "用户自定义模式 (User): " + (useUserCustom ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用"));
            sender.sendMessage(ChatColor.GOLD + "=======================================");

        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "读取配置状态时出错: " + e.getMessage());
        }
    }
}
